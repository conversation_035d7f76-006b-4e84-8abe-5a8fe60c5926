defmodule Drops.MixProject do
  use Mix.Project

  @source_url "https://github.com/solnic/drops"
  @version "0.2.1"
  @license "LGPL-3.0-or-later"

  def project do
    [
      app: :drops,
      version: @version,
      elixir: "~> 1.14",
      start_permanent: Mix.env() == :prod,
      deps: deps(),
      licenses: [@license],
      description: ~S"""
      Tools for working with data effectively - data contracts using types, schemas, domain validation rules, type-safe casting, and more.
      """,
      links: %{"GitHub" => @source_url},
      package: package(),
      docs: docs(),
      source_url: @source_url,
      consolidate_protocols: Mix.env() == :prod,
      elixir_paths: elixir_paths(Mix.env()),
      preferred_cli_env: [
        "drops.example": :dev,
        "drops.examples": :dev,
        drops: :dev
      ],
      aliases: aliases()
    ]
  end

  def elixir_paths(:test) do
    ["lib", "test/support"]
  end

  def elixir_paths(:dev) do
    ["lib", "test/support", "examples"]
  end

  def elixir_paths(_) do
    ["lib"]
  end

  # Run "mix help compile.app" to learn about applications.
  def application do
    [
      mod: {Drops.Application, []},
      extra_applications: [:logger],
      registered: [Drops.Supervisor]
    ]
  end

  defp package() do
    [
      name: "drops",
      files: ~w(lib/drops .formatter.exs mix.exs README* LICENSE CHANGELOG.md),
      licenses: [@license],
      links: %{"GitHub" => "https://github.com/solnic/drops"}
    ]
  end

  defp docs do
    [
      main: "readme",
      source_ref: "v#{@version}",
      extra_section: "GUIDES",
      source_url: @source_url,
      skip_undefined_reference_warnings_on: ["CHANGELOG.md"],
      extras: [
        "README.md",
        "CHANGELOG.md"
      ],
      groups_for_modules: [
        Operations: [
          Drops.Operations,
          Drops.Operations.Command,
          Drops.Operations.Extensions.Command,
          Drops.Operations.Extensions.Params,
          Drops.Operations.Extensions.Ecto
        ],
        Validation: [
          Drops.Contract,
          Drops.Casters,
          Drops.Predicates,
          Drops.Validator.Messages.Backend
        ],
        Types: [
          Drops.Types,
          Drops.Types.Primitive,
          Drops.Types.List,
          Drops.Types.Map,
          Drops.Types.Map.Key,
          Drops.Type.DSL,
          Drops.Types.Union,
          Drops.Types.Cast
        ],
        "Type DSL": [
          Drops.Type,
          Drops.Type.DSL,
          Drops.Type.Validator
        ]
      ]
    ]
  end

  # Run "mix help deps" to learn about dependencies.
  defp deps do
    [
      {:nimble_options, "~> 1.0"},
      {:telemetry, "~> 1.0"},
      {:ex_doc, ">= 0.0.0", only: :dev, runtime: false},
      {:credo, "~> 1.6", only: [:dev, :test], runtime: false},
      {:doctor, "~> 0.21.0", only: :dev},
      {:dialyxir, "~> 1.4", only: [:dev, :test], runtime: false},
      {:igniter, "~> 0.6", optional: true},
      {:ecto, "~> 3.10", optional: true},
      {:ecto_sql, "~> 3.10", optional: true},
      {:ecto_sqlite3, "~> 0.12", only: [:test, :dev]},
      {:logger_file_backend, "~> 0.0.13", only: [:test]},
      {:phoenix_html, "~> 4.0", only: [:test], runtime: false, optional: true},
      {:phoenix_ecto, "~> 4.0", only: [:test], runtime: false, optional: true}
    ]
  end

  # Mix aliases for common tasks
  defp aliases do
    [
      "ecto.setup": ["drops.dev.setup", "ecto.create", "ecto.migrate"],
      "ecto.reset": ["drops.dev.setup", "ecto.drop", "ecto.create", "ecto.migrate"],
      "ecto.migrate": ["drops.dev.setup", "ecto.migrate"],
      "ecto.rollback": ["drops.dev.setup", "ecto.rollback"],
      "ecto.create": ["drops.dev.setup", "ecto.create"],
      "ecto.drop": ["drops.dev.setup", "ecto.drop"],
      "ecto.gen.migration": ["drops.dev.setup", "ecto.gen.migration"]
    ]
  end
end
