{application,exqlite,
             [{modules,['Elixir.DBConnection.Query.Exqlite.Query',
                        'Elixir.Enumerable.Exqlite.Stream','Elixir.Exqlite',
                        'Elixir.Exqlite.Basic','Elixir.Exqlite.Connection',
                        'Elixir.Exqlite.Error','Elixir.Exqlite.Flags',
                        'Elixir.Exqlite.Pragma','Elixir.Exqlite.Query',
                        'Elixir.Exqlite.Result','Elixir.Exqlite.Sqlite3',
                        'Elixir.Exqlite.Sqlite3NIF','Elixir.Exqlite.Stream',
                        'Elixir.Exqlite.TypeExtension',
                        'Elixir.String.Chars.Exqlite.Query']},
              {optional_applications,[table]},
              {applications,[kernel,stdlib,elixir,logger,db_connection,table]},
              {description,"An Elixir SQLite3 library"},
              {registered,[]},
              {vsn,"0.32.1"}]}.
