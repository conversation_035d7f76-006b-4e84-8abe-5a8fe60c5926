{application,glob_ex,
             [{modules,['Elixir.GlobEx','Elixir.GlobEx.CompileError',
                        'Elixir.GlobEx.Compiler','Elixir.GlobEx.Sigils',
                        'Elixir.Inspect.GlobEx']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger]},
              {description,"A library for glob expressions."},
              {registered,[]},
              {vsn,"0.1.11"}]}.
