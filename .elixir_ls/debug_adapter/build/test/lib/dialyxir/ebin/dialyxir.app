{application,dialyxir,
             [{modules,['Elixir.Dialyxir','Elixir.Dialyxir.Dialyzer',
                        'Elixir.Dialyxir.Dialyzer.Runner',
                        'Elixir.Dialyxir.FilterMap',
                        'Elixir.Dialyxir.Formatter',
                        'Elixir.Dialyxir.Formatter.Dialyxir',
                        'Elixir.Dialyxir.Formatter.Dialyzer',
                        'Elixir.Dialyxir.Formatter.Github',
                        'Elixir.Dialyxir.Formatter.IgnoreFile',
                        'Elixir.Dialyxir.Formatter.IgnoreFileStrict',
                        'Elixir.Dialyxir.Formatter.Raw',
                        'Elixir.Dialyxir.Formatter.Short',
                        'Elixir.Dialyxir.Formatter.Utils',
                        'Elixir.Dialyxir.Output','Elixir.Dialyxir.Plt',
                        'Elixir.Dialyxir.Project','Elixir.Dialyxir.Warning',
                        'Elixir.Dialyxir.WarningHelpers',
                        'Elixir.Dialyxir.Warnings',
                        'Elixir.Dialyxir.Warnings.AppCall',
                        'Elixir.Dialyxir.Warnings.Apply',
                        'Elixir.Dialyxir.Warnings.BinaryConstruction',
                        'Elixir.Dialyxir.Warnings.Call',
                        'Elixir.Dialyxir.Warnings.CallToMissingFunction',
                        'Elixir.Dialyxir.Warnings.CallWithOpaque',
                        'Elixir.Dialyxir.Warnings.CallWithoutOpaque',
                        'Elixir.Dialyxir.Warnings.CallbackArgumentTypeMismatch',
                        'Elixir.Dialyxir.Warnings.CallbackInfoMissing',
                        'Elixir.Dialyxir.Warnings.CallbackMissing',
                        'Elixir.Dialyxir.Warnings.CallbackNotExported',
                        'Elixir.Dialyxir.Warnings.CallbackSpecArgumentTypeMismatch',
                        'Elixir.Dialyxir.Warnings.CallbackSpecTypeMismatch',
                        'Elixir.Dialyxir.Warnings.CallbackTypeMismatch',
                        'Elixir.Dialyxir.Warnings.ContractDiff',
                        'Elixir.Dialyxir.Warnings.ContractRange',
                        'Elixir.Dialyxir.Warnings.ContractSubtype',
                        'Elixir.Dialyxir.Warnings.ContractSupertype',
                        'Elixir.Dialyxir.Warnings.ContractWithOpaque',
                        'Elixir.Dialyxir.Warnings.ExactEquality',
                        'Elixir.Dialyxir.Warnings.ExtraRange',
                        'Elixir.Dialyxir.Warnings.FunctionApplicationArguments',
                        'Elixir.Dialyxir.Warnings.FunctionApplicationNoFunction',
                        'Elixir.Dialyxir.Warnings.GuardFail',
                        'Elixir.Dialyxir.Warnings.GuardFailPattern',
                        'Elixir.Dialyxir.Warnings.ImproperListConstruction',
                        'Elixir.Dialyxir.Warnings.InvalidContract',
                        'Elixir.Dialyxir.Warnings.MapUpdate',
                        'Elixir.Dialyxir.Warnings.MissingRange',
                        'Elixir.Dialyxir.Warnings.NegativeGuardFail',
                        'Elixir.Dialyxir.Warnings.NoReturn',
                        'Elixir.Dialyxir.Warnings.OpaqueEquality',
                        'Elixir.Dialyxir.Warnings.OpaqueGuard',
                        'Elixir.Dialyxir.Warnings.OpaqueMatch',
                        'Elixir.Dialyxir.Warnings.OpaqueNonequality',
                        'Elixir.Dialyxir.Warnings.OpaqueTypeTest',
                        'Elixir.Dialyxir.Warnings.OverlappingContract',
                        'Elixir.Dialyxir.Warnings.PatternMatch',
                        'Elixir.Dialyxir.Warnings.PatternMatchCovered',
                        'Elixir.Dialyxir.Warnings.RecordConstruction',
                        'Elixir.Dialyxir.Warnings.RecordMatch',
                        'Elixir.Dialyxir.Warnings.RecordMatching',
                        'Elixir.Dialyxir.Warnings.UnknownBehaviour',
                        'Elixir.Dialyxir.Warnings.UnknownFunction',
                        'Elixir.Dialyxir.Warnings.UnknownType',
                        'Elixir.Dialyxir.Warnings.UnmatchedReturn',
                        'Elixir.Dialyxir.Warnings.UnusedFunction',
                        'Elixir.Mix.Tasks.Dialyzer',
                        'Elixir.Mix.Tasks.Dialyzer.Build',
                        'Elixir.Mix.Tasks.Dialyzer.Clean',
                        'Elixir.Mix.Tasks.Dialyzer.Explain']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,dialyzer,crypto,mix,erts,
                             syntax_tools,logger,erlex]},
              {description,"Mix tasks to simplify use of Dialyzer in Elixir projects.\n"},
              {registered,[]},
              {vsn,"1.4.5"},
              {mod,{'Elixir.Dialyxir',[]}}]}.
