defmodule Mix.Tasks.Drops.Example do
  @moduledoc """
  Runs Drops examples with proper environment setup and timing measurement.

  ## Usage

      mix drops.example examples/readme/schemas-01.ex
      mix drops.example examples/ecto/save_user_operation.exs
      mix drops.example examples/contract/schema-01.exs

  This task automatically:
  - Runs in dev environment with examples support
  - Ensures dependencies are available
  - Runs the example file
  - Measures and displays execution time

  ## Examples

  Run a basic schema example:

      mix drops.example examples/readme/schemas-01.ex

  Run an Ecto operation example:

      mix drops.example examples/ecto/save_user_operation.exs

  Run a contract validation example:

      mix drops.example examples/contract/schema-01.exs

  ## Timing Display

  The task measures execution time and displays it in an appropriate format:
  - Milliseconds for durations under 1 second (e.g., "25ms")
  - Seconds with 2 decimal places for durations under 1 minute (e.g., "1.50s")
  - Minutes and seconds for longer durations (e.g., "2m 30s")

  ## Available Examples

  You can list all available examples by running:

      mix drops.examples

  """

  use Mix.Task

  @shortdoc "Runs a Drops example with proper environment setup and timing measurement"

  @impl Mix.Task
  def run([]) do
    Mix.shell().info("""
    Usage: mix drops.example <example_file>

    Examples:
      mix drops.example examples/readme/schemas-01.ex
      mix drops.example examples/ecto/save_user_operation.exs
      mix drops.example examples/contract/schema-01.exs

    To see all available examples, run: mix drops.examples
    """)
  end

  def run([example_path | _rest]) do
    # Validate the example file exists
    unless File.exists?(example_path) do
      Mix.shell().error("Example file not found: #{example_path}")
      Mix.shell().info("To see all available examples, run: mix drops.examples")
      System.halt(1)
    end

    # Set up the development environment
    Mix.Task.run("drops.dev.setup")

    # Run the example with timing
    Mix.shell().info("Running example: #{example_path}")
    Mix.shell().info(String.duplicate("=", 60))

    start_time = System.monotonic_time()

    try do
      Code.eval_file(example_path)
    rescue
      error ->
        Mix.shell().error("Error running example: #{inspect(error)}")
        System.halt(1)
    end

    end_time = System.monotonic_time()
    duration_ms = System.convert_time_unit(end_time - start_time, :native, :millisecond)

    Mix.shell().info(String.duplicate("=", 60))
    Mix.shell().info("Example completed successfully!")
    Mix.shell().info("Execution time: #{format_duration(duration_ms)}")
  end

  defp format_duration(duration_ms) when duration_ms < 1000 do
    "#{duration_ms}ms"
  end

  defp format_duration(duration_ms) when duration_ms < 60_000 do
    seconds = duration_ms / 1000
    "#{:erlang.float_to_binary(seconds, decimals: 2)}s"
  end

  defp format_duration(duration_ms) do
    total_seconds = div(duration_ms, 1000)
    minutes = div(total_seconds, 60)
    seconds = rem(total_seconds, 60)
    "#{minutes}m #{seconds}s"
  end
end
