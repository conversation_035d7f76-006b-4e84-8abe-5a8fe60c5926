defmodule Mix.Tasks.Drops.Dev.Setup do
  @moduledoc """
  Sets up the development environment for Drops.

  This task ensures that the test support files are loaded and the development
  environment is properly configured for running examples and ecto tasks.

  ## Usage

      mix drops.dev.setup

  This task is automatically called by other development tasks like ecto commands
  and example runners, so you typically don't need to run it manually.

  ## What it does

  - Loads test/support/setup.exs which contains all necessary setup
  - Ensures dependencies are started
  - Sets up the test repository for development use
  - Configures the environment for examples and database operations

  """

  use Mix.Task

  @shortdoc "Sets up the development environment for Drops"

  @impl Mix.Task
  def run(_args) do
    # Ensure we're in the right environment
    unless Mix.env() in [:dev, :test] do
      Mix.raise("drops.dev.setup can only be run in :dev or :test environment")
    end

    # Load the common setup file
    setup_file = "test/support/setup.exs"

    unless File.exists?(setup_file) do
      Mix.raise("Setup file not found: #{setup_file}")
    end

    # Require the setup file which handles all the initialization
    Code.require_file(setup_file)

    {:ok, _} = Application.ensure_all_started(:ecto_sql)

    Mix.shell().info("Development environment setup complete")
  end
end
