defmodule Drops.Relation.Schema.Field do
  @moduledoc """
  Represents a field in a database table/schema.
  
  This struct stores comprehensive information about a database field including
  its name, type information, and source mapping.
  
  ## Examples
  
      # Simple field
      %Drops.Relation.Schema.Field{
        name: :email,
        type: :string,
        ecto_type: :string,
        source: :email
      }
      
      # Field with different source mapping
      %Drops.Relation.Schema.Field{
        name: :user_id,
        type: :integer,
        ecto_type: :id,
        source: :user_id
      }
  """
  
  @type t :: %__MODULE__{
    name: atom(),
    type: atom(),
    ecto_type: term(),
    source: atom()
  }
  
  defstruct [:name, :type, :ecto_type, :source]
  
  @doc """
  Creates a new Field struct.
  
  ## Parameters
  
  - `name` - The field name as an atom
  - `type` - The normalized type (e.g., :string, :integer)
  - `ecto_type` - The original Ecto type
  - `source` - The source column name in the database
  
  ## Examples
  
      iex> Drops.Relation.Schema.Field.new(:email, :string, :string, :email)
      %Drops.Relation.Schema.Field{
        name: :email,
        type: :string,
        ecto_type: :string,
        source: :email
      }
  """
  @spec new(atom(), atom(), term(), atom()) :: t()
  def new(name, type, ecto_type, source) do
    %__MODULE__{
      name: name,
      type: type,
      ecto_type: ecto_type,
      source: source
    }
  end
  
  @doc """
  Creates a Field struct from field metadata map.
  
  ## Parameters
  
  - `field_metadata` - A map with :name, :type, :ecto_type, and :source keys
  
  ## Examples
  
      iex> metadata = %{name: :email, type: :string, ecto_type: :string, source: :email}
      iex> Drops.Relation.Schema.Field.from_metadata(metadata)
      %Drops.Relation.Schema.Field{
        name: :email,
        type: :string,
        ecto_type: :string,
        source: :email
      }
  """
  @spec from_metadata(map()) :: t()
  def from_metadata(%{name: name, type: type, ecto_type: ecto_type, source: source}) do
    new(name, type, ecto_type, source)
  end
  
  @doc """
  Converts a Field struct to field metadata map.
  
  ## Examples
  
      iex> field = Drops.Relation.Schema.Field.new(:email, :string, :string, :email)
      iex> Drops.Relation.Schema.Field.to_metadata(field)
      %{name: :email, type: :string, ecto_type: :string, source: :email}
  """
  @spec to_metadata(t()) :: map()
  def to_metadata(%__MODULE__{name: name, type: type, ecto_type: ecto_type, source: source}) do
    %{
      name: name,
      type: type,
      ecto_type: ecto_type,
      source: source
    }
  end
  
  @doc """
  Checks if two fields have the same name.
  
  ## Examples
  
      iex> field1 = Drops.Relation.Schema.Field.new(:email, :string, :string, :email)
      iex> field2 = Drops.Relation.Schema.Field.new(:email, :text, :text, :email_address)
      iex> Drops.Relation.Schema.Field.same_name?(field1, field2)
      true
  """
  @spec same_name?(t(), t()) :: boolean()
  def same_name?(%__MODULE__{name: name1}, %__MODULE__{name: name2}) do
    name1 == name2
  end
  
  @doc """
  Checks if a field matches a given name.
  
  ## Examples
  
      iex> field = Drops.Relation.Schema.Field.new(:email, :string, :string, :email)
      iex> Drops.Relation.Schema.Field.matches_name?(field, :email)
      true
      
      iex> Drops.Relation.Schema.Field.matches_name?(field, :name)
      false
  """
  @spec matches_name?(t(), atom()) :: boolean()
  def matches_name?(%__MODULE__{name: field_name}, name) when is_atom(name) do
    field_name == name
  end
end
