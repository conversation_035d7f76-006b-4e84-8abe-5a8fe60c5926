defmodule Drops.Relation.Cache do
  @moduledoc """
  Convenience module for managing Drops.Relation schema cache.

  This module provides a simplified interface for common cache operations
  without needing to interact with the SchemaCache GenServer directly.

  ## Examples

      # Clear cache for a specific repository
      Drops.Relation.Cache.clear(MyApp.Repo)

      # Clear all cached schemas
      Drops.Relation.Cache.clear_all()

      # Get cache statistics
      stats = Drops.Relation.Cache.stats()

      # Check if cache is enabled
      if Drops.Relation.Cache.enabled?() do
        # Cache-specific logic
      end

      # Warm up cache for specific tables
      Drops.Relation.Cache.warm_up(MyApp.Repo, ["users", "posts", "comments"])
  """

  alias Drops.Relation.SchemaCache
  alias Drops.Config

  @doc """
  Clears all cached schemas for a specific repository.

  ## Parameters

  - `repo` - The Ecto repository module to clear cache for

  ## Examples

      Drops.Relation.Cache.clear(MyApp.Repo)
  """
  @spec clear(module()) :: :ok
  def clear(repo) when is_atom(repo) do
    SchemaCache.clear_repo_cache(repo)
  end

  @doc """
  Clears the entire schema cache for all repositories.

  ## Examples

      Drops.Relation.Cache.clear_all()
  """
  @spec clear_all() :: :ok
  def clear_all do
    SchemaCache.clear_all()
  end

  @doc """
  Returns cache statistics including hit/miss ratios and entry counts.

  ## Returns

  A map containing:
  - `:total_entries` - Total number of cached schemas
  - `:hits` - Number of cache hits since start
  - `:misses` - Number of cache misses since start
  - `:hit_ratio` - Cache hit ratio as a percentage

  ## Examples

      %{
        total_entries: 15,
        hits: 142,
        misses: 8,
        hit_ratio: 94.67
      } = Drops.Relation.Cache.stats()
  """
  @spec stats() :: map()
  def stats do
    SchemaCache.stats()
  end

  @doc """
  Checks if schema caching is enabled.

  ## Returns

  Returns `true` if caching is enabled, `false` otherwise.

  ## Examples

      if Drops.Relation.Cache.enabled?() do
        IO.puts("Cache is enabled")
      end
  """
  @spec enabled?() :: boolean()
  def enabled? do
    Config.schema_cache()[:enabled]
  end

  @doc """
  Returns the current cache configuration.

  ## Returns

  A keyword list with the current cache configuration.

  ## Examples

      config = Drops.Relation.Cache.config()
      # => [enabled: true, max_entries: 1000, cleanup_interval: 1800000]
  """
  @spec config() :: keyword()
  def config do
    Config.schema_cache()
  end

  @doc """
  Warms up the cache by pre-loading schemas for specified tables.

  This function forces schema inference for the given tables and caches
  the results. This can be useful during application startup to ensure
  frequently used schemas are cached.

  ## Parameters

  - `repo` - The Ecto repository module
  - `table_names` - List of table names to warm up

  ## Returns

  Returns `:ok` on success, or `{:error, reason}` if warming up fails.

  ## Examples

      # Warm up cache for common tables
      Drops.Relation.Cache.warm_up(MyApp.Repo, ["users", "posts", "comments"])

      # Warm up cache for all tables in a repository
      tables = MyApp.Repo.query!("SELECT tablename FROM pg_tables WHERE schemaname = 'public'").rows
      table_names = Enum.map(tables, &List.first/1)
      Drops.Relation.Cache.warm_up(MyApp.Repo, table_names)
  """
  @spec warm_up(module(), [String.t()]) :: :ok | {:error, term()}
  def warm_up(repo, table_names) when is_atom(repo) and is_list(table_names) do
    if enabled?() do
      try do
        Enum.each(table_names, fn table_name ->
          # Force inference by calling get_or_infer_schema with a dummy function
          SchemaCache.get_or_infer_schema(repo, table_name, fn ->
            # This will only be called if not cached
            # We create a minimal relation module for inference
            relation_module = create_temp_relation_module(table_name)
            Drops.Relation.Inference.infer_schema(relation_module, table_name, repo)
          end)
        end)

        :ok
      rescue
        error -> {:error, error}
      end
    else
      {:error, :cache_disabled}
    end
  end

  @doc """
  Inspects the cache contents for debugging purposes.

  Returns a list of all cached entries with their metadata.

  ## Returns

  A list of maps, each containing:
  - `:repo` - The repository module
  - `:table_name` - The table name
  - `:migration_version` - The migration version when cached
  - `:cached_at` - When the entry was cached (if available)

  ## Examples

      entries = Drops.Relation.Cache.inspect()
      # => [
      #   %{repo: MyApp.Repo, table_name: "users", migration_version: "20231201120000"},
      #   %{repo: MyApp.Repo, table_name: "posts", migration_version: "20231201120000"}
      # ]
  """
  @spec inspect() :: [map()]
  def inspect do
    if enabled?() do
      # This is a simplified inspection - in a real implementation,
      # you might want to add more metadata to the cache entries
      try do
        # Get all cache entries (this would require adding a function to SchemaCache)
        # For now, we'll return basic stats
        stats = stats()

        [
          %{
            message: "Cache inspection not fully implemented",
            total_entries: stats.total_entries,
            suggestion: "Use Drops.Relation.Cache.stats() for basic information"
          }
        ]
      rescue
        _ -> []
      end
    else
      []
    end
  end

  @doc """
  Forces a refresh of cached schemas for a repository.

  This clears the cache for the repository and then optionally warms it up
  again with the specified table names.

  ## Parameters

  - `repo` - The Ecto repository module
  - `table_names` - Optional list of table names to warm up after clearing

  ## Examples

      # Just clear the cache
      Drops.Relation.Cache.refresh(MyApp.Repo)

      # Clear and warm up specific tables
      Drops.Relation.Cache.refresh(MyApp.Repo, ["users", "posts"])
  """
  @spec refresh(module(), [String.t()] | nil) :: :ok | {:error, term()}
  def refresh(repo, table_names \\ nil) when is_atom(repo) do
    clear(repo)

    case table_names do
      nil -> :ok
      names when is_list(names) -> warm_up(repo, names)
    end
  end

  ## Private Functions

  defp create_temp_relation_module(table_name) do
    # Create a temporary module name for inference
    # This is a bit of a hack, but allows us to reuse the existing inference logic
    module_name = String.to_atom("TempRelation#{String.capitalize(table_name)}")

    # We don't actually need to create the module, just return the name
    # The inference function will work with just the module name
    module_name
  end
end
