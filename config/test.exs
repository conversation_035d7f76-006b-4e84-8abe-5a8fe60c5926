import Config

# Ensure log directory exists
log_dir = Path.join(File.cwd!(), "log")

# Configure logger to write Ecto logs to file
config :logger,
  backends: [
    {LoggerFileBackend, :ecto_test},
    {LoggerFileBackend, :drops_test}
  ]

# Configure Ecto file logger
config :logger, :ecto_test,
  path: Path.join(log_dir, "ecto_test.log"),
  level: :info,
  format: "$time $metadata[$level] $message\n",
  metadata: [:query_time, :decode_time, :queue_time, :connection_time]

# Configure Drops file logger
config :logger, :drops_test,
  path: Path.join(log_dir, "test.log"),
  level: :info,
  format: "$time $metadata[$level] $message\n"

# Configure the test repository
config :drops, Drops.TestRepo,
  adapter: Ecto.Adapters.SQLite3,
  database: ":memory:",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 1,
  queue_target: 5000,
  queue_interval: 1000,
  log: :info,
  loggers: [{Ecto.LogEntry, :log, [:ecto_test]}]

# Configure Ecto repos
config :drops, :ecto_repos, [Drops.TestRepo]

# Configure schema cache for test environment
config :drops, :schema_cache,
  enabled: true,
  max_entries: 100,
  cleanup_interval: :never
