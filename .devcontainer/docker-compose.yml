services:
  base: &base
    build:
      context: .
      dockerfile: Dockerfile
    working_dir: /workspace/drops
    command: sleep infinity
    volumes: [ "..:/workspace/drops" ]

  postgres:
    image: postgres:latest
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DATABASE: postgres
      POSTGRES_USERNAME: postgres
    ports:
      - 5432:5432
    command: [ "postgres", "-c", "log_statement=all" ]
    volumes: [ "repobot_pgdata:/var/lib/postgresql/data" ]

  dev-latest: &dev-latest
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ELIXIR_VERSION: 1.18.4-otp-27

  dev-1.17:
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ELIXIR_VERSION: 1.17-otp-27

  dev-1.16:
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ELIXIR_VERSION: 1.16-otp-26

  dev-1.15:
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ELIXIR_VERSION: 1.15-otp-25

  dev-1.14:
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ELIXIR_VERSION: 1.14-otp-24

  test:
    <<: *dev-latest

volumes:
  drops_pgdata:
