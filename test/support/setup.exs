Code.require_file("test_repo.ex", __DIR__)
Code.require_file("test_config.ex", __DIR__)

Code.require_file("doctest_case.ex", __DIR__)
Code.require_file("data_case.ex", __DIR__)
Code.require_file("contract_case.ex", __DIR__)
Code.require_file("operation_case.ex", __DIR__)
Code.require_file("relation_case.ex", __DIR__)

Code.require_file("ecto/test_schemas.ex", __DIR__)
Code.require_file("ecto/user_group_schemas.ex", __DIR__)

Application.ensure_all_started(:drops)
