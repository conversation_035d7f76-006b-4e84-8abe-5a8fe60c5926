{application,elixir_make,
             [{modules,['Elixir.ElixirMake.Artefact',
                        'Elixir.ElixirMake.Compiler',
                        'Elixir.ElixirMake.Downloader',
                        'Elixir.ElixirMake.Downloader.Httpc',
                        'Elixir.ElixirMake.Precompiler',
                        'Elixir.Mix.Tasks.Compile.ElixirMake',
                        'Elixir.Mix.Tasks.ElixirMake.Checksum',
                        'Elixir.Mix.Tasks.ElixirMake.Precompile']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger]},
              {description,"A Make compiler for Mix"},
              {registered,[]},
              {vsn,"0.9.0"}]}.
