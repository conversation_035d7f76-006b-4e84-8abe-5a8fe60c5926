{<<"links">>,[{<<"GitHub">>,<<"https://github.com/elixir-ecto/ecto">>}]}.
{<<"name">>,<<"ecto">>}.
{<<"version">>,<<"3.13.2">>}.
{<<"description">>,
 <<"A toolkit for data mapping and language integrated query for Elixir">>}.
{<<"elixir">>,<<"~> 1.14">>}.
{<<"files">>,
 [<<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,<<"CHANGELOG.md">>,
  <<"lib">>,<<"lib/ecto">>,<<"lib/ecto/adapter.ex">>,<<"lib/ecto/multi.ex">>,
  <<"lib/ecto/queryable.ex">>,<<"lib/ecto/type.ex">>,
  <<"lib/ecto/changeset.ex">>,<<"lib/ecto/parameterized_type.ex">>,
  <<"lib/ecto/changeset">>,<<"lib/ecto/changeset/relation.ex">>,
  <<"lib/ecto/adapter">>,<<"lib/ecto/adapter/transaction.ex">>,
  <<"lib/ecto/adapter/queryable.ex">>,<<"lib/ecto/adapter/schema.ex">>,
  <<"lib/ecto/adapter/storage.ex">>,<<"lib/ecto/query.ex">>,
  <<"lib/ecto/enum.ex">>,<<"lib/ecto/schema">>,
  <<"lib/ecto/schema/metadata.ex">>,<<"lib/ecto/schema/loader.ex">>,
  <<"lib/ecto/uuid.ex">>,<<"lib/ecto/json.ex">>,<<"lib/ecto/embedded.ex">>,
  <<"lib/ecto/schema.ex">>,<<"lib/ecto/association.ex">>,
  <<"lib/ecto/application.ex">>,<<"lib/ecto/query">>,
  <<"lib/ecto/query/planner.ex">>,<<"lib/ecto/query/window_api.ex">>,
  <<"lib/ecto/query/api.ex">>,<<"lib/ecto/query/inspect.ex">>,
  <<"lib/ecto/query/builder.ex">>,<<"lib/ecto/query/builder">>,
  <<"lib/ecto/query/builder/preload.ex">>,
  <<"lib/ecto/query/builder/group_by.ex">>,
  <<"lib/ecto/query/builder/join.ex">>,<<"lib/ecto/query/builder/update.ex">>,
  <<"lib/ecto/query/builder/distinct.ex">>,
  <<"lib/ecto/query/builder/limit_offset.ex">>,
  <<"lib/ecto/query/builder/select.ex">>,
  <<"lib/ecto/query/builder/windows.ex">>,
  <<"lib/ecto/query/builder/from.ex">>,<<"lib/ecto/query/builder/cte.ex">>,
  <<"lib/ecto/query/builder/combination.ex">>,
  <<"lib/ecto/query/builder/dynamic.ex">>,
  <<"lib/ecto/query/builder/order_by.ex">>,
  <<"lib/ecto/query/builder/lock.ex">>,<<"lib/ecto/query/builder/filter.ex">>,
  <<"lib/ecto/repo.ex">>,<<"lib/ecto/repo">>,
  <<"lib/ecto/repo/transaction.ex">>,<<"lib/ecto/repo/supervisor.ex">>,
  <<"lib/ecto/repo/registry.ex">>,<<"lib/ecto/repo/queryable.ex">>,
  <<"lib/ecto/repo/preloader.ex">>,<<"lib/ecto/repo/schema.ex">>,
  <<"lib/ecto/repo/assoc.ex">>,<<"lib/ecto/exceptions.ex">>,<<"lib/mix">>,
  <<"lib/mix/tasks">>,<<"lib/mix/tasks/ecto.create.ex">>,
  <<"lib/mix/tasks/ecto.ex">>,<<"lib/mix/tasks/ecto.gen.repo.ex">>,
  <<"lib/mix/tasks/ecto.drop.ex">>,<<"lib/mix/ecto.ex">>,<<"lib/ecto.ex">>,
  <<"integration_test/cases">>,<<"integration_test/cases/type.exs">>,
  <<"integration_test/cases/interval.exs">>,
  <<"integration_test/cases/preload.exs">>,
  <<"integration_test/cases/assoc.exs">>,
  <<"integration_test/cases/joins.exs">>,
  <<"integration_test/cases/windows.exs">>,
  <<"integration_test/cases/repo.exs">>,<<"integration_test/support">>,
  <<"integration_test/support/schemas.exs">>,
  <<"integration_test/support/types.exs">>]}.
{<<"app">>,<<"ecto">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"telemetry">>},
   {<<"app">>,<<"telemetry">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.4 or ~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"decimal">>},
   {<<"app">>,<<"decimal">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 2.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"jason">>},
   {<<"app">>,<<"jason">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.
