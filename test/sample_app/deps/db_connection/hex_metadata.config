{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/elixir-ecto/db_connection">>}]}.
{<<"name">>,<<"db_connection">>}.
{<<"version">>,<<"2.8.0">>}.
{<<"description">>,
 <<"Database connection behaviour for database transactions and connection pooling">>}.
{<<"elixir">>,<<"~> 1.11">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/db_connection">>,<<"lib/db_connection/backoff.ex">>,
  <<"lib/db_connection/log_entry.ex">>,<<"lib/db_connection/ownership.ex">>,
  <<"lib/db_connection/connection_pool">>,
  <<"lib/db_connection/connection_pool/pool.ex">>,
  <<"lib/db_connection/task.ex">>,<<"lib/db_connection/connection_error.ex">>,
  <<"lib/db_connection/query.ex">>,<<"lib/db_connection/holder.ex">>,
  <<"lib/db_connection/ownership">>,
  <<"lib/db_connection/ownership/proxy.ex">>,
  <<"lib/db_connection/ownership/manager.ex">>,
  <<"lib/db_connection/watcher.ex">>,<<"lib/db_connection/pool.ex">>,
  <<"lib/db_connection/connection_pool.ex">>,
  <<"lib/db_connection/connection.ex">>,
  <<"lib/db_connection/telemetry_listener.ex">>,
  <<"lib/db_connection/app.ex">>,<<"lib/db_connection.ex">>,
  <<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,<<"CHANGELOG.md">>]}.
{<<"app">>,<<"db_connection">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"telemetry">>},
   {<<"app">>,<<"telemetry">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.4 or ~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.
