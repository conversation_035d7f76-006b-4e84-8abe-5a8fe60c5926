{<<"links">>,
 [{<<"Changelog">>,
   <<"https://github.com/elixir-sqlite/exqlite/blob/main/CHANGELOG.md">>},
  {<<"GitHub">>,<<"https://github.com/elixir-sqlite/exqlite">>}]}.
{<<"name">>,<<"exqlite">>}.
{<<"version">>,<<"0.32.1">>}.
{<<"description">>,<<"An Elixir SQLite3 library">>}.
{<<"elixir">>,<<"~> 1.14">>}.
{<<"app">>,<<"exqlite">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/exqlite.ex">>,<<"lib/exqlite">>,<<"lib/exqlite/error.ex">>,
  <<"lib/exqlite/flags.ex">>,<<"lib/exqlite/pragma.ex">>,
  <<"lib/exqlite/query.ex">>,<<"lib/exqlite/result.ex">>,
  <<"lib/exqlite/stream.ex">>,<<"lib/exqlite/basic.ex">>,
  <<"lib/exqlite/connection.ex">>,<<"lib/exqlite/type_extension.ex">>,
  <<"lib/exqlite/sqlite3.ex">>,<<"lib/exqlite/sqlite3_nif.ex">>,
  <<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,<<"LICENSE">>,
  <<".clang-format">>,<<"c_src">>,<<"c_src/sqlite3ext.h">>,
  <<"c_src/sqlite3.c">>,<<"c_src/sqlite3.h">>,<<"c_src/sqlite3_nif.c">>,
  <<"Makefile">>,<<"Makefile.win">>,<<"checksum.exs">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"db_connection">>},
   {<<"app">>,<<"db_connection">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 2.1">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"elixir_make">>},
   {<<"app">>,<<"elixir_make">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.8">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"cc_precompiler">>},
   {<<"app">>,<<"cc_precompiler">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.1">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"table">>},
   {<<"app">>,<<"table">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 0.1.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>,<<"make">>]}.
