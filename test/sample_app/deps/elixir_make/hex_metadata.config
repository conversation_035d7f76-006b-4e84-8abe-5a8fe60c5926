{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/elixir-lang/elixir_make">>}]}.
{<<"name">>,<<"elixir_make">>}.
{<<"version">>,<<"0.9.0">>}.
{<<"description">>,<<"A Make compiler for Mix">>}.
{<<"elixir">>,<<"~> 1.9">>}.
{<<"app">>,<<"elixir_make">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"files">>,
 [<<"lib">>,<<"lib/elixir_make">>,<<"lib/elixir_make/artefact.ex">>,
  <<"lib/elixir_make/compiler.ex">>,<<"lib/elixir_make/downloader">>,
  <<"lib/elixir_make/downloader/httpc.ex">>,
  <<"lib/elixir_make/downloader.ex">>,<<"lib/elixir_make/precompiler.ex">>,
  <<"lib/mix">>,<<"lib/mix/tasks">>,
  <<"lib/mix/tasks/elixir_make.checksum.ex">>,
  <<"lib/mix/tasks/elixir_make.precompile.ex">>,
  <<"lib/mix/tasks/compile.elixir_make.ex">>,<<".formatter.exs">>,
  <<"mix.exs">>,<<"README.md">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.
