{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/elixir-sqlite/ecto_sqlite3">>},
  {<<"docs">>,<<"https://hexdocs.pm/ecto_sqlite3">>}]}.
{<<"name">>,<<"ecto_sqlite3">>}.
{<<"version">>,<<"0.21.0">>}.
{<<"description">>,<<"An SQLite3 Ecto3 adapter.">>}.
{<<"elixir">>,<<"~> 1.15">>}.
{<<"app">>,<<"ecto_sqlite3">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/ecto">>,<<"lib/ecto/adapters">>,
  <<"lib/ecto/adapters/sqlite3">>,<<"lib/ecto/adapters/sqlite3/codec.ex">>,
  <<"lib/ecto/adapters/sqlite3/data_type.ex">>,
  <<"lib/ecto/adapters/sqlite3/connection.ex">>,
  <<"lib/ecto/adapters/sqlite3/type_extension.ex">>,
  <<"lib/ecto/adapters/sqlite3.ex">>,<<".formatter.exs">>,<<"mix.exs">>,
  <<"README.md">>,<<"LICENSE">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"decimal">>},
   {<<"app">>,<<"decimal">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.6 or ~> 2.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"ecto_sql">>},
   {<<"app">>,<<"ecto_sql">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 3.13.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"ecto">>},
   {<<"app">>,<<"ecto">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 3.13.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"exqlite">>},
   {<<"app">>,<<"exqlite">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.22">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.
