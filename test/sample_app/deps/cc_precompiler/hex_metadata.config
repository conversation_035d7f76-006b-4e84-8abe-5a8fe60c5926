{<<"links">>,
 [{<<"Changelog">>,
   <<"https://github.com/cocoa-xu/cc_precompiler/blob/v0.1.10/CHANGELOG.md">>},
  {<<"GitHub">>,<<"https://github.com/cocoa-xu/cc_precompiler">>},
  {<<"Precompilation Guide">>,
   <<"https://github.com/cocoa-xu/cc_precompiler/blob/v0.1.10/PRECOMPILATION_GUIDE.md">>},
  {<<"Readme">>,
   <<"https://github.com/cocoa-xu/cc_precompiler/blob/v0.1.10/README.md">>}]}.
{<<"name">>,<<"cc_precompiler">>}.
{<<"version">>,<<"0.1.10">>}.
{<<"description">>,
 <<"NIF library Precompiler that uses C/C++ (cross-)compiler.">>}.
{<<"elixir">>,<<"~> 1.11">>}.
{<<"app">>,<<"cc_precompiler">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/cc_precompiler.ex">>,<<"lib/compilation_script">>,
  <<"lib/compilation_script/universal_binary.ex">>,
  <<"lib/compilation_script/compilation_script.ex">>,<<"mix.exs">>,
  <<"README.md">>,<<"LICENSE">>,<<"CHANGELOG.md">>,
  <<"PRECOMPILATION_GUIDE.md">>]}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"elixir_make">>},
   {<<"app">>,<<"elixir_make">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.7 or ~> 0.8">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.
