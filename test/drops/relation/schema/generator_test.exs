defmodule Drops.Relation.Schema.GeneratorTest do
  use ExUnit.Case, async: true

  alias Drops.Relation.Schema.Generator
  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.{Field, PrimaryKey}

  describe "generate_module_content/3" do
    test "generates basic schema content without defmodule" do
      schema = %Schema{
        source: "users",
        primary_key: %PrimaryKey{fields: [Field.new(:id, :integer, :id, :id)]},
        fields: [
          Field.new(:id, :integer, :id, :id),
          Field.new(:email, :string, :string, :email),
          Field.new(:age, :integer, :integer, :age)
        ],
        foreign_keys: [],
        indices: %Drops.Relation.Schema.Indices{indices: []},
        virtual_fields: []
      }

      result = Generator.generate_module_content("MyApp.User", "users", schema)

      # Should not include defmodule since Igniter adds it
      refute result =~ "defmodule"
      assert result =~ "use Ecto.Schema"
      assert result =~ ~s(schema "users" do)
      assert result =~ "field :email, :string"
      assert result =~ "field :age, :integer"
      assert result =~ "timestamps()"
      # Should be excluded as it's the default primary key
      refute result =~ "field :id"
    end

    test "generates schema content with custom primary key" do
      schema = %Schema{
        source: "organizations",
        primary_key: %PrimaryKey{fields: [Field.new(:org_id, :integer, :id, :org_id)]},
        fields: [
          Field.new(:org_id, :integer, :id, :org_id),
          Field.new(:name, :string, :string, :name)
        ],
        foreign_keys: [],
        indices: %Drops.Relation.Schema.Indices{indices: []},
        virtual_fields: []
      }

      result =
        Generator.generate_module_content("MyApp.Organization", "organizations", schema)

      assert result =~ "@primary_key {:org_id, :id, autogenerate: true}"
      assert result =~ "field :name, :string"
      # Should be excluded as it's the primary key
      refute result =~ "field :org_id"
    end

    test "generates schema content with composite primary key" do
      schema = %Schema{
        source: "user_roles",
        primary_key: %PrimaryKey{
          fields: [
            Field.new(:user_id, :integer, :id, :user_id),
            Field.new(:role_id, :integer, :id, :role_id)
          ]
        },
        fields: [
          Field.new(:user_id, :integer, :id, :user_id),
          Field.new(:role_id, :integer, :id, :role_id),
          Field.new(:granted_at, :naive_datetime, :naive_datetime, :granted_at)
        ],
        foreign_keys: [],
        indices: %Drops.Relation.Schema.Indices{indices: []},
        virtual_fields: []
      }

      result = Generator.generate_module_content("MyApp.UserRole", "user_roles", schema)

      assert result =~ "@primary_key [{:user_id, :id}, {:role_id, :id}]"
      assert result =~ "field :granted_at, :naive_datetime"
      refute result =~ "field :user_id"
      refute result =~ "field :role_id"
    end
  end

  describe "generate_field_definitions/2" do
    test "generates field definitions excluding primary key and timestamps" do
      fields = [
        Field.new(:id, :integer, :id, :id),
        Field.new(:email, :string, :string, :email),
        Field.new(:age, :integer, :integer, :age),
        Field.new(:inserted_at, :naive_datetime, :naive_datetime, :inserted_at),
        Field.new(:updated_at, :naive_datetime, :naive_datetime, :updated_at)
      ]

      primary_key = %PrimaryKey{fields: [Field.new(:id, :integer, :id, :id)]}

      result = Generator.generate_field_definitions(fields, primary_key)

      assert result =~ "field :email, :string"
      assert result =~ "field :age, :integer"
      refute result =~ "field :id"
      refute result =~ "field :inserted_at"
      refute result =~ "field :updated_at"
    end

    test "generates field with source option when different from name" do
      fields = [
        Field.new(:email_address, :string, :string, :email)
      ]

      result = Generator.generate_field_definitions(fields)

      assert result =~ "field :email_address, :string, source: :email"
    end

    test "handles complex field types" do
      fields = [
        Field.new(:tags, {:array, :string}, {:array, :string}, :tags),
        Field.new(:metadata, :map, :map, :metadata),
        Field.new(:score, :float, :float, :score)
      ]

      result = Generator.generate_field_definitions(fields)

      assert result =~ "field :tags, {:array, :string}"
      assert result =~ "field :metadata, :map"
      assert result =~ "field :score, :float"
    end
  end

  describe "generate_primary_key_attribute/1" do
    test "returns empty string for default primary key" do
      primary_key = %PrimaryKey{fields: [Field.new(:id, :integer, :id, :id)]}

      result = Generator.generate_primary_key_attribute(primary_key)

      assert result == ""
    end

    test "generates custom primary key attribute" do
      primary_key = %PrimaryKey{fields: [Field.new(:uuid, :binary, :binary_id, :uuid)]}

      result = Generator.generate_primary_key_attribute(primary_key)

      assert result == "  @primary_key {:uuid, :binary_id, autogenerate: true}\n"
    end

    test "generates composite primary key attribute" do
      primary_key = %PrimaryKey{
        fields: [
          Field.new(:user_id, :integer, :id, :user_id),
          Field.new(:role_id, :integer, :id, :role_id)
        ]
      }

      result = Generator.generate_primary_key_attribute(primary_key)

      assert result == "  @primary_key [{:user_id, :id}, {:role_id, :id}]\n"
    end

    test "generates no primary key attribute" do
      primary_key = %PrimaryKey{fields: []}

      result = Generator.generate_primary_key_attribute(primary_key)

      assert result == "  @primary_key false\n"
    end
  end
end
